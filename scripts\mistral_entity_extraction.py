#!/usr/bin/env python3
"""
FalkorDB-compatible Mistral OCR Entity Extraction Script
Uses Mistral OCR for document processing and entity extraction with strong prompts.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import json

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.mistral_ocr import MistralOCRProcessor
from utils.config import get_config
from database.database_service import get_falkordb_adapter, create_episode_node, create_fact_node, create_entity_node
from services.embedding_processor import EmbeddingProcessor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MistralEntityExtractor:
    """Enhanced entity extraction using Mistral OCR with strong prompts."""

    def __init__(self):
        """Initialize the Mistral entity extractor."""
        self.config = get_config()
        self.mistral_ocr = None
        self.embedding_processor = None

        # Initialize Mistral OCR
        import os
        mistral_api_key = os.getenv('MISTRAL_API_KEY')
        if mistral_api_key:
            try:
                self.mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
                logger.info("✅ Mistral OCR initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Mistral OCR: {e}")
        else:
            logger.error("❌ No Mistral API key found")

        # Initialize embedding processor
        try:
            self.embedding_processor = EmbeddingProcessor()
            logger.info("✅ Embedding processor initialized")
        except Exception as e:
            logger.warning(f"⚠️ Embedding processor initialization failed: {e}")

    def get_entity_extraction_prompt(self, text: str) -> str:
        """Generate strong prompt for entity extraction."""
        return f"""You are an expert medical and scientific knowledge extraction AI. Extract entities and relationships from the following text with high precision.

EXTRACTION RULES:
1. Focus on MEDICAL, HEALTH, NUTRITIONAL, and SCIENTIFIC entities
2. Extract entities that are explicitly mentioned in the text
3. Categorize entities using these specific types:
   - Herb: Medicinal plants, botanical remedies
   - Nutrient: Vitamins, minerals, compounds
   - Disease: Medical conditions, disorders
   - Medication: Pharmaceutical drugs, treatments
   - Symptom: Clinical manifestations
   - Process: Biological/chemical processes
   - Treatment: Therapeutic approaches
   - Research: Studies, trials, methodologies
   - Organization: Institutions, companies
   - Person: Researchers, authors
   - Food: Dietary items, food groups
   - Chemical: Chemical compounds
   - Protein: Protein molecules
   - Plant: Plant species
   - Hormone: Endocrine molecules

RELATIONSHIP TYPES:
- TREATS: X treats Y
- CAUSES: X causes Y
- PREVENTS: X prevents Y
- CONTAINS: X contains Y
- INTERACTS_WITH: X interacts with Y
- INCREASES: X increases Y
- DECREASES: X decreases Y
- INHIBITS: X inhibits Y
- ACTIVATES: X activates Y
- DERIVED_FROM: X derived from Y
- USED_FOR: X used for Y
- ASSOCIATED_WITH: X associated with Y

TEXT TO ANALYZE:
{text}

Return ONLY a valid JSON object with this exact structure:
{{
    "entities": [
        {{
            "name": "exact entity name from text",
            "type": "one of the specified types above",
            "description": "brief description based on context",
            "confidence": 0.95
        }}
    ],
    "relationships": [
        {{
            "source": "source entity name",
            "target": "target entity name",
            "type": "relationship type from list above",
            "description": "brief description of relationship",
            "confidence": 0.90
        }}
    ]
}}

IMPORTANT: Return ONLY the JSON object, no additional text or formatting."""

    async def extract_entities_from_text(self, text: str) -> Dict[str, Any]:
        """Extract entities from text using Mistral with strong prompts."""
        if not self.mistral_ocr:
            logger.error("Mistral OCR not available")
            return {"entities": [], "relationships": []}

        try:
            # Limit text size for processing
            if len(text) > 8000:
                text = text[:8000]
                logger.info(f"Truncated text to 8000 characters for entity extraction")

            # Skip very short text
            if len(text.strip()) < 50:
                logger.info("Text too short for entity extraction")
                return {"entities": [], "relationships": []}

            prompt = self.get_entity_extraction_prompt(text)

            # Use OpenRouter client for entity extraction (more reliable)
            from utils.open_router_client import OpenRouterClient
            import os

            openrouter_api_key = os.getenv('OPENROUTER_API_KEY') or os.getenv('OPEN_ROUTER_API_KEY')
            if not openrouter_api_key:
                logger.error("No OpenRouter API key found")
                return {"entities": [], "relationships": []}

            client = OpenRouterClient(
                api_key=openrouter_api_key,
                model="meta-llama/llama-4-maverick"
            )

            response_text = client.generate_completion(
                system_prompt="You are an expert medical and scientific knowledge extraction AI.",
                user_prompt=prompt,
                temperature=0.1,
                max_tokens=2000
            )

            # Parse JSON response
            try:
                # Clean up response if it has markdown formatting
                if "```json" in response_text:
                    response_text = response_text.split("```json")[1].split("```")[0].strip()
                elif "```" in response_text:
                    response_text = response_text.split("```")[1].split("```")[0].strip()

                extracted_data = json.loads(response_text)

                # Validate structure
                if not isinstance(extracted_data, dict):
                    raise ValueError("Response is not a dictionary")

                entities = extracted_data.get("entities", [])
                relationships = extracted_data.get("relationships", [])

                logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships")
                return extracted_data

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.debug(f"Raw response: {response_text[:500]}...")
                return {"entities": [], "relationships": []}

        except Exception as e:
            logger.error(f"Error in entity extraction: {e}")
            return {"entities": [], "relationships": []}

    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """Process a document with Mistral OCR and extract entities."""
        logger.info(f"Processing document: {file_path}")

        if not self.mistral_ocr:
            return {"success": False, "error": "Mistral OCR not available"}

        try:
            # Extract text using Mistral OCR
            logger.info("Extracting text with Mistral OCR...")
            ocr_result = await self.mistral_ocr.process_pdf(file_path)

            if not ocr_result.get("success"):
                return {"success": False, "error": f"OCR failed: {ocr_result.get('error')}"}

            text = ocr_result.get("text", "")
            if not text:
                return {"success": False, "error": "No text extracted"}

            logger.info(f"Extracted {len(text)} characters of text")

            # Extract entities
            logger.info("Extracting entities...")
            entities_data = await self.extract_entities_from_text(text)

            return {
                "success": True,
                "text": text,
                "entities": entities_data.get("entities", []),
                "relationships": entities_data.get("relationships", []),
                "text_length": len(text)
            }

        except Exception as e:
            logger.error(f"Error processing document: {e}")
            return {"success": False, "error": str(e)}

    async def process_and_store_document(self, file_path: str, episode_id: str = None) -> Dict[str, Any]:
        """Process document and store entities in FalkorDB."""
        result = await self.process_document(file_path)

        if not result["success"]:
            return result

        try:
            db = get_falkordb_adapter()

            # Create episode if not provided
            if not episode_id:
                document_name = Path(file_path).stem
                episode_id = await create_episode_node(
                    f"Document: {document_name}",
                    {
                        'uuid': f"doc_{document_name}_{int(asyncio.get_event_loop().time())}",
                        'source_file': file_path,
                        'processed_with': 'mistral_ocr'
                    }
                )
                logger.info(f"Created episode: {episode_id}")

            # Store entities
            entity_count = 0
            for entity in result["entities"]:
                try:
                    await create_entity_node(
                        entity["name"],
                        entity["type"],
                        {
                            'uuid': f"entity_{entity['name'].replace(' ', '_')}_{int(asyncio.get_event_loop().time())}",
                            'description': entity.get("description", ""),
                            'confidence': entity.get("confidence", 0.0),
                            'source_document': file_path,
                            'episode_id': episode_id
                        }
                    )
                    entity_count += 1
                except Exception as e:
                    logger.warning(f"Failed to store entity {entity['name']}: {e}")

            logger.info(f"✅ Stored {entity_count} entities in FalkorDB")

            result["stored_entities"] = entity_count
            result["episode_id"] = episode_id
            return result

        except Exception as e:
            logger.error(f"Error storing entities: {e}")
            result["storage_error"] = str(e)
            return result

async def main():
    """Main function to test entity extraction."""
    import argparse

    parser = argparse.ArgumentParser(description="Extract entities from PDF using Mistral OCR")
    parser.add_argument("file_path", help="Path to PDF file")
    parser.add_argument("--output", help="Output JSON file path")
    parser.add_argument("--store", action="store_true", help="Store entities in FalkorDB")

    args = parser.parse_args()

    if not os.path.exists(args.file_path):
        logger.error(f"File not found: {args.file_path}")
        return

    # Initialize extractor
    extractor = MistralEntityExtractor()

    # Process document
    if args.store:
        result = await extractor.process_and_store_document(args.file_path)
    else:
        result = await extractor.process_document(args.file_path)

    if result["success"]:
        logger.info(f"✅ Successfully processed {args.file_path}")
        logger.info(f"📊 Extracted {len(result['entities'])} entities")
        logger.info(f"🔗 Extracted {len(result['relationships'])} relationships")

        if args.store and "stored_entities" in result:
            logger.info(f"💾 Stored {result['stored_entities']} entities in FalkorDB")

        # Save results if output path provided
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Results saved to {args.output}")

        # Print sample entities
        for i, entity in enumerate(result['entities'][:5]):
            logger.info(f"Entity {i+1}: {entity['name']} ({entity['type']})")

    else:
        logger.error(f"❌ Failed to process {args.file_path}: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
