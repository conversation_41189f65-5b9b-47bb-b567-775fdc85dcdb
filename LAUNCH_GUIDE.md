# 🚀 Graphiti Launch Guide - Unified Pathways

## 📋 **SINGLE SOURCE OF TRUTH FOR LAUNCHING GRAPHITI**

This guide provides the **only** launch methods for Graphiti after comprehensive cleanup and unification.

---

## 🎯 **PRIORITY 1: MCP Implementation (Recommended)**

### **Docker MCP Deployment**
```bash
# Start MCP server with all dependencies
docker-compose -f docker-compose.unified.yml up graphiti-mcp falkordb redis-stack ollama

# Access MCP server
curl http://localhost:8000/health
```

### **Local MCP Development**
```bash
# Navigate to MCP server
cd mcp_server

# Install dependencies
uv sync

# Run MCP server
uv run graphiti_mcp_server.py --transport sse
```

---

## 🎯 **PRIORITY 2: Traditional UI (Fallback)**

### **Docker UI Deployment**
```bash
# Start UI server with all dependencies
docker-compose -f docker-compose.unified.yml up graphiti-ui falkordb redis-stack ollama

# Access UI
open http://localhost:9753
```

### **Local UI Development**
```bash
# Install dependencies
pip install -r requirements.txt

# Run UI server
python app.py
```

---

## 🔧 **Configuration**

### **Environment Variables**
Create `.env` file with:
```env
# Database
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
FALKORDB_PASSWORD=Triathlon16!

# API Keys
OPENAI_API_KEY=your_openai_key
OPENROUTER_API_KEY=your_openrouter_key

# LLM Configuration
QA_LLM_MODEL=meta-llama/llama-4-maverick
QA_LLM_TEMPERATURE=0.7
QA_LLM_TOP_P=0.9

# Embedding Configuration
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=snowflake-arctic-embed2
```

---

## 📊 **Service Endpoints**

| Service | URL | Purpose |
|---------|-----|---------|
| **MCP Server** | http://localhost:8000 | Modern MCP implementation |
| **UI Server** | http://localhost:9753 | Traditional web interface |
| **FalkorDB** | localhost:6379 | Graph database |
| **Redis Stack** | localhost:6380 | Vector search |
| **Ollama** | localhost:11434 | Local LLM/embeddings |

---

## ⚠️ **REMOVED LAUNCH METHODS**

The following launch methods have been **PERMANENTLY REMOVED** to eliminate inconsistencies:

- ❌ `launch_graphiti.py` - Redundant wrapper
- ❌ `server/graph_service/main.py` - Competing FastAPI app
- ❌ Multiple docker-compose files - Consolidated to unified
- ❌ Legacy scripts in root directory - Moved to scripts/

---

## 🎉 **Quick Start**

### **For MCP Development:**
```bash
docker-compose -f docker-compose.unified.yml up graphiti-mcp -d
```

### **For UI Development:**
```bash
python app.py
```

### **For Full Stack:**
```bash
docker-compose -f docker-compose.unified.yml up -d
```

---

## 📞 **Support**

- **MCP Issues**: Check `mcp_server/README.md`
- **UI Issues**: Check application logs
- **Database Issues**: Check FalkorDB connection
- **Docker Issues**: Check `docker-compose.unified.yml`

**This is the ONLY launch guide. All other launch instructions are deprecated.**
