#!/usr/bin/env python3
"""
Unified start script for Graphiti - Single entry point for all launch methods.
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path

def start_ui_server():
    """Start the traditional UI server."""
    print("🚀 Starting Graphiti UI Server...")
    print("📍 Access at: http://localhost:9753")
    print("📊 Dashboard: http://localhost:9753/")
    print("📚 API Docs: http://localhost:9753/docs")
    print("🔧 Settings: http://localhost:9753/settings")
    print("=" * 50)
    
    try:
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Graphiti UI Server stopped.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start UI server: {e}")
        sys.exit(1)

def start_mcp_server():
    """Start the MCP server."""
    print("🚀 Starting Graphiti MCP Server...")
    print("📍 Access at: http://localhost:8000")
    print("🔗 MCP Protocol: SSE Transport")
    print("=" * 50)
    
    mcp_dir = Path("mcp_server")
    if not mcp_dir.exists():
        print("❌ MCP server directory not found!")
        sys.exit(1)
    
    try:
        os.chdir(mcp_dir)
        subprocess.run(["uv", "run", "graphiti_mcp_server.py", "--transport", "sse"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Graphiti MCP Server stopped.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start MCP server: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ 'uv' command not found. Please install uv or run MCP server manually.")
        print("💡 Alternative: cd mcp_server && python graphiti_mcp_server.py --transport sse")
        sys.exit(1)

def start_docker():
    """Start with Docker Compose."""
    print("🐳 Starting Graphiti with Docker...")
    print("📍 UI: http://localhost:9753")
    print("📍 MCP: http://localhost:8000")
    print("=" * 50)
    
    try:
        subprocess.run([
            "docker-compose", 
            "-f", "docker-compose.unified.yml", 
            "up", "--build"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 Docker services stopped.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Docker services: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ 'docker-compose' command not found. Please install Docker Compose.")
        sys.exit(1)

def run_health_check():
    """Run system health check."""
    print("🔍 Running Graphiti Health Check...")
    print("=" * 50)
    
    try:
        subprocess.run([sys.executable, "scripts/health_check.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Health check failed: {e}")
        sys.exit(1)

def setup_database():
    """Set up the database."""
    print("🔧 Setting up Graphiti Database...")
    print("=" * 50)
    
    try:
        subprocess.run([sys.executable, "scripts/setup_database.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Database setup failed: {e}")
        sys.exit(1)

def show_status():
    """Show system status."""
    print("📊 Graphiti System Status")
    print("=" * 50)
    
    # Check if services are running
    import requests
    
    services = [
        ("UI Server", "http://localhost:9753/health"),
        ("MCP Server", "http://localhost:8000/health"),
    ]
    
    for name, url in services:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print(f"✅ {name}: Running")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
        except:
            print(f"❌ {name}: Not running")

def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(
        description="Unified start script for Graphiti",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start.py ui              # Start UI server (default)
  python start.py mcp             # Start MCP server
  python start.py docker          # Start with Docker
  python start.py health          # Run health check
  python start.py setup           # Set up database
  python start.py status          # Show system status

For more information, see LAUNCH_GUIDE.md
        """
    )
    
    parser.add_argument(
        "mode",
        nargs="?",
        default="ui",
        choices=["ui", "mcp", "docker", "health", "setup", "status"],
        help="Launch mode (default: ui)"
    )
    
    args = parser.parse_args()
    
    # Check if .env file exists
    if not Path(".env").exists() and args.mode not in ["health", "status"]:
        print("⚠️ .env file not found!")
        print("💡 Copy .env.example to .env and configure your settings.")
        print("   cp .env.example .env")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    # Route to appropriate function
    if args.mode == "ui":
        start_ui_server()
    elif args.mode == "mcp":
        start_mcp_server()
    elif args.mode == "docker":
        start_docker()
    elif args.mode == "health":
        run_health_check()
    elif args.mode == "setup":
        setup_database()
    elif args.mode == "status":
        show_status()

if __name__ == "__main__":
    main()
